package adhoc.mock

import org.web3j.abi.EventEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Event
import org.web3j.abi.datatypes.Utf8String
import org.web3j.abi.datatypes.generated.Bytes32
import org.web3j.protocol.core.methods.response.Log

/**
 * Factory class for creating mock event objects used in testing.
 * This class centralizes the creation of various mock objects related to blockchain events,
 * improving maintainability and reusability across test classes.
 *
 * Note: This class provides data and configuration for mocks, but the actual Mock() creation
 * must be done within Spock test classes.
 */
class EventMockFactory {


	/**
	 * Create a mock AddProviderRole event definition for testing
	 * Event: AddProviderRole(bytes32 indexed providerId, address providerEoa, bytes32 traceId)
	 * @return Event definition for AddProviderRole
	 */
	static Event createMockAddProviderRoleEvent() {
		def parameters = [
			// providerId (indexed)
			new TypeReference<Bytes32>(true) {},
			// providerEoa (non-indexed)
			new TypeReference<Address>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("AddProviderRole", parameters)
	}

	/**
	 * Create a mock AddTokenByProvider event definition for testing
	 * Event: AddTokenByProvider(bytes32 indexed providerId, bytes32 tokenId, bytes32 traceId)
	 * @return Event definition for AddTokenByProvider
	 */
	static Event createMockAddTokenByProviderEvent() {
		def parameters = [
			// providerId (indexed)
			new TypeReference<Bytes32>(true) {},
			// tokenId (non-indexed)
			new TypeReference<Bytes32>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("AddTokenByProvider", parameters)
	}

	/**
	 * Create a mock AddRoleAdminChanged event definition for testing
	 * Event: AddRoleAdminChanged(bytes32 indexed role, bytes32 indexed previousAdminRole, bytes32 indexed newAdminRole)
	 * @return Event definition for AddRoleAdminChanged
	 */
	static Event createMockAddRoleAdminChangedEvent() {
		def parameters = [
			// role (indexed)
			new TypeReference<Bytes32>(true) {},
			// previousAdminRole (indexed)
			new TypeReference<Bytes32>(true) {},
			// newAdminRole (indexed)
			new TypeReference<Bytes32>(true) {}
		]
		return new Event("RoleAdminChanged", parameters)
	}

	/**
	 * Create a mock RoleGranted event definition for testing
	 * Event: RoleGranted(bytes32 indexed role, address indexed account, address indexed sender)
	 * @return Event definition for RoleGranted
	 */
	static Event createMockRoleGrantedEvent() {
		def parameters = [
			// role (indexed)
			new TypeReference<Bytes32>(true) {},
			// account (indexed)
			new TypeReference<Address>(true) {},
			// sender (indexed)
			new TypeReference<Address>(true) {}
		]
		return new Event("RoleGranted", parameters)
	}

	/**
	 * Create a mock RoleRevoked event definition for testing
	 * Event: RoleRevoked(bytes32 indexed role, address indexed account, address indexed sender)
	 * @return Event definition for RoleRevoked
	 */
	static Event createMockRoleRevokedEvent() {
		def parameters = [
			// role (indexed)
			new TypeReference<Bytes32>(true) {},
			// account (indexed)
			new TypeReference<Address>(true) {},
			// sender (indexed)
			new TypeReference<Address>(true) {}
		]
		return new Event("RoleRevoked", parameters)
	}

	/**
	 * Create a mock ModProvider event definition for testing
	 * Event: ModProvider(bytes32 indexed providerId, bytes32 name, bytes32 traceId)
	 * @return Event definition for ModProvider
	 */
	static Event createMockModProviderEvent() {
		def parameters = [
			// providerId (indexed)
			new TypeReference<Bytes32>(true) {},
			// name (non-indexed)
			new TypeReference<Bytes32>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("ModProvider", parameters)
	}

	/**
	 * Create a mock ModAccount event definition for testing
	 * Event: ModAccount(bytes32 accountId, string accountName, bytes32 traceId)
	 * @return Event definition for ModAccount
	 */
	static Event createMockModAccountEvent() {
		def parameters = [
			// accountId (non-indexed)
			new TypeReference<Bytes32>(false) {},
			// accountName (non-indexed)
			new TypeReference<Utf8String>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("ModAccount", parameters)
	}

	/**
	 * Create a proper AddProviderRole log for normal testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with AddProviderRole event data
	 */
	static Log createAddProviderRoleLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

		// Calculate AddProviderRole event signature using Web3j
		def addProviderRoleEvent = createMockAddProviderRoleEvent()
		def eventSignature = EventEncoder.encode(addProviderRoleEvent)
		println("AddProviderRole event signature: ${eventSignature}")

		// providerId (indexed parameter)
		def providerId = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"

		log.topics = [eventSignature, providerId]

		// Data contains: address providerEoa + bytes32 traceId
		// providerEoa: 0xa1b2c3d4e5f6789012345678901234567890abcd (20 bytes, padded to 32)
		// traceId: 0xef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcd (32 bytes)
		log.data = "0x000000000000000000000000a1b2c3d4e5f6789012345678901234567890abcd" +
				"ef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcd"

		return log
	}

	/**
	 * Create a proper AddTokenByProvider log for normal testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with AddTokenByProvider event data
	 */
	static Log createAddTokenByProviderLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

		// Calculate AddTokenByProvider event signature using Web3j
		def addTokenByProviderEvent = createMockAddTokenByProviderEvent()
		def eventSignature = EventEncoder.encode(addTokenByProviderEvent)
		println("AddTokenByProvider event signature: ${eventSignature}")

		// providerId (indexed parameter)
		def providerId = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

		log.topics = [eventSignature, providerId]

		// Data contains: bytes32 tokenId + bytes32 traceId
		// tokenId: 0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba
		// traceId: 0x1111222233334444555566667777888899990000aaaabbbbccccddddeeeeffff
		log.data = "0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba" +
				"1111222233334444555566667777888899990000aaaabbbbccccddddeeeeffff"

		return log
	}

	/**
	 * Create a proper RoleAdminChanged log for case empty traceId in non-indexed values
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with RoleAdminChanged event data
	 */
	static Log createRoleAdminChangedLog() {
		def log = new Log()

		// Use AccessControl contract address
		log.address = "0xF27289E45825f7E8F3eAE5c3F52e05c8FB6fD3d4"

		// Calculate RoleAdminChanged event signature using Web3j
		def event = createMockAddRoleAdminChangedEvent()
		def eventSignature = EventEncoder.encode(event)
		println("RoleAdminChanged event signature: ${eventSignature}")

		def role = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
		def previousAdminRole = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
		def newAdminRole = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

		log.topics = [
			eventSignature,
			role,
			previousAdminRole,
			newAdminRole
		]

		return log
	}

	/**
	 * Create a proper RoleGranted log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with RoleGranted event data
	 */
	static Log createMockRoleGrantedLog() {
		def log = new Log()

		// Use AccessControl contract address
		log.address = "0xF27289E45825f7E8F3eAE5c3F52e05c8FB6fD3d4"

		// Calculate RoleGranted event signature using Web3j
		def event = createMockRoleGrantedEvent()
		def eventSignature = EventEncoder.encode(event)
		println("RoleGranted event signature: ${eventSignature}")

		def role = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
		def account = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
		def sender = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

		log.topics = [
			eventSignature,
			role,
			account,
			sender
		]

		return log
	}

	/**
	 * Create a proper RoleRevoked log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with RoleRevoked event data
	 */
	static Log createMockRoleRevokedLog() {
		def log = new Log()

		// Use AccessControl contract address
		log.address = "0xF27289E45825f7E8F3eAE5c3F52e05c8FB6fD3d4"

		// Calculate RoleRevoked event signature using Web3j
		def event = createMockRoleRevokedEvent()
		def eventSignature = EventEncoder.encode(event)
		println("RoleRevoked event signature: ${eventSignature}")

		def role = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
		def account = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
		def sender = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

		log.topics = [
			eventSignature,
			role,
			account,
			sender
		]

		return log
	}

	/** Create a proper ModProvider log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with ModProvider event data
	 */
	static Log createMockModProviderLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

		// Calculate ModProvider event signature using Web3j
		def modProviderEvent = createMockModProviderEvent()
		def eventSignature = EventEncoder.encode(modProviderEvent)
		println("ModProvider event signature: ${eventSignature}")

		// providerId (indexed parameter)
		def providerId = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

		log.topics = [eventSignature, providerId]

		// Data contains: bytes32 name + bytes32 traceId
		// name: 0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba
		// traceId: 0x1111222233334444555566667777888899990000aaaabbbbccccddddeeeeffff
		log.data = "0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba" +
				"1111222233334444555566667777888899990000aaaabbbbccccddddeeeeffff"

		return log
	}

	/**
	 * Create a proper ModAccount log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with ModAccount event data
	 */
	static Log createMockModAccountLog() {
		def log = new Log()

		// Use Account contract address
		log.address = "0x993366A606A99129e56B4b99B27e428ba1Cb672f"

		// Calculate ModAccount event signature using Web3j
		def modAccountEvent = createMockModAccountEvent()
		def eventSignature = EventEncoder.encode(modAccountEvent)
		println("ModAccount event signature: ${eventSignature}")

		// ModAccount has no indexed parameters, so topics only contains event signature
		log.topics = [eventSignature]

		// Data contains: bytes32 accountId + string accountName + bytes32 traceId
		// accountId: 0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef (32 bytes)
		// accountName: "TestAccount" encoded as string (dynamic length)
		// traceId: 0xef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcd (32 bytes)

		// For string encoding in ABI:
		// - First 32 bytes: offset to string data (0x60 = 96 bytes, pointing after accountId + offset + length)
		// - Next 32 bytes: accountId
		// - Next 32 bytes: traceId
		// - Then string data: length (32 bytes) + actual string data (padded to 32-byte boundary)
		def accountId = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def traceId = "ef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcd"
		def accountName = "TestAccount"
		def accountNameHex = accountName.bytes.encodeHex().toString().padRight(64, '0')
		def accountNameLength = String.format("%064x", accountName.length())

		log.data = "0x" +
				"0000000000000000000000000000000000000000000000000000000000000060" + // offset to string (96 bytes)
				accountId + // accountId
				traceId + // traceId
				accountNameLength + // string length
				accountNameHex // string data

		return log
	}
}
